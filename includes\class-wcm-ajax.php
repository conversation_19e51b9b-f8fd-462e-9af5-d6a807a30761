<?php
/**
 * AJAX functionality for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WCM_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX actions for logged-in users
        add_action('wp_ajax_wcm_delete_expense', array($this, 'delete_expense'));
        add_action('wp_ajax_wcm_get_chart_data', array($this, 'get_chart_data'));
        add_action('wp_ajax_wcm_refresh_dashboard', array($this, 'refresh_dashboard'));
        add_action('wp_ajax_wcm_get_expense', array($this, 'get_expense'));

        // POS AJAX actions
        add_action('wp_ajax_wcm_pos_search_products', array($this, 'pos_search_products'));
        add_action('wp_ajax_wcm_pos_add_to_cart', array($this, 'pos_add_to_cart'));
        add_action('wp_ajax_wcm_pos_update_cart_item', array($this, 'pos_update_cart_item'));
        add_action('wp_ajax_wcm_pos_remove_cart_item', array($this, 'pos_remove_cart_item'));
        add_action('wp_ajax_wcm_pos_clear_cart', array($this, 'pos_clear_cart'));
        add_action('wp_ajax_wcm_pos_calculate_totals', array($this, 'pos_calculate_totals'));
        add_action('wp_ajax_wcm_pos_process_order', array($this, 'pos_process_order'));
    }
    
    /**
     * Delete expense via AJAX
     */
    public function delete_expense() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $expense_id = intval($_POST['expense_id']);
        
        if (!$expense_id) {
            wp_send_json_error('Invalid expense ID');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $result = $wpdb->delete(
            $table_name,
            array('id' => $expense_id),
            array('%d')
        );
        
        if ($result) {
            wp_send_json_success('Expense deleted successfully');
        } else {
            wp_send_json_error('Failed to delete expense');
        }
    }
    
    /**
     * Get chart data via AJAX
     */
    public function get_chart_data() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $period = sanitize_text_field($_POST['period']);
        
        switch ($period) {
            case 'yearly':
                $data = $this->get_yearly_data();
                break;
            case 'quarterly':
                $data = $this->get_quarterly_data();
                break;
            default:
                $data = $this->get_monthly_data();
                break;
        }
        
        wp_send_json_success($data);
    }
    
    /**
     * Refresh dashboard data via AJAX
     */
    public function refresh_dashboard() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $cash_data = $this->get_cash_data();
        
        wp_send_json_success($cash_data);
    }

    /**
     * Get single expense via AJAX
     */
    public function get_expense() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $expense_id = intval($_POST['expense_id']);

        if (!$expense_id) {
            wp_send_json_error('Invalid expense ID');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        $expense = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $expense_id
        ));

        if ($expense) {
            wp_send_json_success($expense);
        } else {
            wp_send_json_error('Expense not found');
        }
    }
    
    /**
     * Get cash data
     */
    private function get_cash_data() {
        $income = $this->get_total_income();
        $expenses = $this->get_total_expenses();
        $balance = $income - $expenses;
        
        return array(
            'income' => wc_price($income),
            'expenses' => wc_price($expenses),
            'balance' => wc_price($balance),
            'balance_raw' => $balance
        );
    }
    
    /**
     * Get total income from WooCommerce orders (HPOS Compatible)
     */
    private function get_total_income() {
        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled, use direct database query for better performance
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'"
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method for legacy stores
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get total expenses
     */
    private function get_total_expenses() {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $total = $wpdb->get_var("SELECT SUM(amount) FROM $table_name");
        
        return $total ? $total : 0;
    }
    
    /**
     * Get monthly data for charts
     */
    private function get_monthly_data() {
        $months = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $months[] = date('M Y', strtotime("-$i months"));
            
            // Get income for this month
            $income_data[] = $this->get_monthly_income($date);
            
            // Get expenses for this month
            $expense_data[] = $this->get_monthly_expenses($date);
        }
        
        return array(
            'labels' => $months,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get yearly data for charts
     */
    private function get_yearly_data() {
        $years = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 5 years
        for ($i = 4; $i >= 0; $i--) {
            $year = date('Y', strtotime("-$i years"));
            $years[] = $year;
            
            // Get income for this year
            $income_data[] = $this->get_yearly_income($year);
            
            // Get expenses for this year
            $expense_data[] = $this->get_yearly_expenses($year);
        }
        
        return array(
            'labels' => $years,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get quarterly data for charts
     */
    private function get_quarterly_data() {
        $quarters = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 8 quarters
        for ($i = 7; $i >= 0; $i--) {
            $quarter_start = date('Y-m-d', strtotime("-$i quarters"));
            $quarter = 'Q' . ceil(date('n', strtotime($quarter_start)) / 3) . ' ' . date('Y', strtotime($quarter_start));
            $quarters[] = $quarter;
            
            // Get income for this quarter
            $income_data[] = $this->get_quarterly_income($quarter_start);
            
            // Get expenses for this quarter
            $expense_data[] = $this->get_quarterly_expenses($quarter_start);
        }
        
        return array(
            'labels' => $quarters,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get monthly income (HPOS Compatible)
     */
    private function get_monthly_income($month) {
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get monthly expenses
     */
    private function get_monthly_expenses($month) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
    
    /**
     * Get yearly income (HPOS Compatible)
     */
    private function get_yearly_income($year) {
        $start_date = $year . '-01-01';
        $end_date = $year . '-12-31';

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get yearly expenses
     */
    private function get_yearly_expenses($year) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = $year . '-01-01';
        $end_date = $year . '-12-31';
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
    
    /**
     * Get quarterly income (HPOS Compatible)
     */
    private function get_quarterly_income($quarter_start) {
        $start_date = date('Y-m-d', strtotime($quarter_start));
        $end_date = date('Y-m-d', strtotime($quarter_start . ' +3 months -1 day'));

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get quarterly expenses
     */
    private function get_quarterly_expenses($quarter_start) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = date('Y-m-d', strtotime($quarter_start));
        $end_date = date('Y-m-d', strtotime($quarter_start . ' +3 months -1 day'));
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }

    /**
     * Search products for POS
     */
    public function pos_search_products() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $search = sanitize_text_field($_POST['search']);
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;

        $args = array(
            'status' => 'publish',
            'limit' => $limit,
            'stock_status' => 'instock',
            'type' => array('simple', 'variable')
        );

        if (!empty($search)) {
            $args['s'] = $search;
        }

        $products = wc_get_products($args);
        $pos_products = array();

        foreach ($products as $product) {
            $product_data = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'price' => $product->get_price(),
                'regular_price' => $product->get_regular_price(),
                'sale_price' => $product->get_sale_price(),
                'stock_quantity' => $product->get_stock_quantity(),
                'manage_stock' => $product->get_manage_stock(),
                'stock_status' => $product->get_stock_status(),
                'sku' => $product->get_sku(),
                'type' => $product->get_type(),
                'image_url' => wp_get_attachment_image_url($product->get_image_id(), 'medium'),
                'variations' => array()
            );

            // Handle variable products
            if ($product->is_type('variable')) {
                $variations = $product->get_available_variations();
                foreach ($variations as $variation) {
                    $variation_obj = wc_get_product($variation['variation_id']);
                    if ($variation_obj && $variation_obj->is_in_stock()) {
                        $product_data['variations'][] = array(
                            'id' => $variation['variation_id'],
                            'attributes' => $variation['attributes'],
                            'price' => $variation_obj->get_price(),
                            'stock_quantity' => $variation_obj->get_stock_quantity(),
                            'sku' => $variation_obj->get_sku()
                        );
                    }
                }
            }

            $pos_products[] = $product_data;
        }

        wp_send_json_success($pos_products);
    }

    /**
     * Add product to cart
     */
    public function pos_add_to_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $product_id = intval($_POST['product_id']);
        $variation_id = isset($_POST['variation_id']) ? intval($_POST['variation_id']) : 0;
        $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;

        if (!$product_id) {
            wp_send_json_error('Invalid product ID');
        }

        // Get or create cart session
        $cart = $this->get_pos_cart();

        // Create cart item key
        $cart_item_key = $variation_id ? $product_id . '_' . $variation_id : $product_id;

        // Get product
        $product = wc_get_product($variation_id ? $variation_id : $product_id);
        if (!$product) {
            wp_send_json_error('Product not found');
        }

        // Check stock
        if ($product->get_manage_stock() && $product->get_stock_quantity() < $quantity) {
            wp_send_json_error('Insufficient stock');
        }

        // Add or update cart item
        if (isset($cart[$cart_item_key])) {
            $cart[$cart_item_key]['quantity'] += $quantity;
        } else {
            $cart[$cart_item_key] = array(
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'quantity' => $quantity,
                'price' => $product->get_price(),
                'name' => $product->get_name(),
                'image_url' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail')
            );
        }

        // Save cart
        $this->save_pos_cart($cart);

        // Calculate totals
        $totals = $this->calculate_pos_totals($cart);

        wp_send_json_success(array(
            'cart' => $cart,
            'totals' => $totals,
            'message' => 'Product added to cart'
        ));
    }

    /**
     * Update cart item quantity
     */
    public function pos_update_cart_item() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
        $quantity = intval($_POST['quantity']);

        if (!$cart_item_key || $quantity < 0) {
            wp_send_json_error('Invalid parameters');
        }

        $cart = $this->get_pos_cart();

        if (!isset($cart[$cart_item_key])) {
            wp_send_json_error('Cart item not found');
        }

        if ($quantity == 0) {
            unset($cart[$cart_item_key]);
        } else {
            // Check stock
            $product_id = $cart[$cart_item_key]['variation_id'] ? $cart[$cart_item_key]['variation_id'] : $cart[$cart_item_key]['product_id'];
            $product = wc_get_product($product_id);

            if ($product && $product->get_manage_stock() && $product->get_stock_quantity() < $quantity) {
                wp_send_json_error('Insufficient stock');
            }

            $cart[$cart_item_key]['quantity'] = $quantity;
        }

        $this->save_pos_cart($cart);
        $totals = $this->calculate_pos_totals($cart);

        wp_send_json_success(array(
            'cart' => $cart,
            'totals' => $totals
        ));
    }

    /**
     * Remove cart item
     */
    public function pos_remove_cart_item() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);

        if (!$cart_item_key) {
            wp_send_json_error('Invalid cart item key');
        }

        $cart = $this->get_pos_cart();

        if (isset($cart[$cart_item_key])) {
            unset($cart[$cart_item_key]);
        }

        $this->save_pos_cart($cart);
        $totals = $this->calculate_pos_totals($cart);

        wp_send_json_success(array(
            'cart' => $cart,
            'totals' => $totals
        ));
    }

    /**
     * Clear cart
     */
    public function pos_clear_cart() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $this->save_pos_cart(array());

        wp_send_json_success(array(
            'cart' => array(),
            'totals' => array(
                'subtotal' => 0,
                'tax' => 0,
                'total' => 0
            )
        ));
    }

    /**
     * Calculate cart totals
     */
    public function pos_calculate_totals() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $cart = $this->get_pos_cart();
        $totals = $this->calculate_pos_totals($cart);

        wp_send_json_success($totals);
    }

    /**
     * Process POS order
     */
    public function pos_process_order() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_pos_nonce')) {
            wp_die('Security check failed');
        }

        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }

        $payment_method = sanitize_text_field($_POST['payment_method']);
        $customer_email = sanitize_email($_POST['customer_email']);

        if (!$payment_method) {
            wp_send_json_error('Payment method is required');
        }

        $cart = $this->get_pos_cart();

        if (empty($cart)) {
            wp_send_json_error('Cart is empty');
        }

        try {
            // Create WooCommerce order
            $order = wc_create_order();

            // Add products to order
            foreach ($cart as $cart_item) {
                $product_id = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
                $product = wc_get_product($product_id);

                if ($product) {
                    $order->add_product($product, $cart_item['quantity']);
                }
            }

            // Set customer email if provided
            if (!empty($customer_email)) {
                $order->set_billing_email($customer_email);
            }

            // Set payment method
            $order->set_payment_method($payment_method);

            // Calculate totals
            $order->calculate_totals();

            // Set order status based on payment method
            if ($payment_method === 'cash') {
                $order->update_status('completed', 'POS Cash Payment');

                // Add cash transaction to cash manager
                $this->add_cash_transaction($order->get_total(), $order->get_id());
            } else {
                $order->update_status('processing', 'POS Payment');
            }

            // Clear cart
            $this->save_pos_cart(array());

            wp_send_json_success(array(
                'order_id' => $order->get_id(),
                'order_number' => $order->get_order_number(),
                'total' => $order->get_total(),
                'message' => 'Order processed successfully'
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to create order: ' . $e->getMessage());
        }
    }

    /**
     * Get POS cart from session
     */
    private function get_pos_cart() {
        if (!session_id()) {
            session_start();
        }

        return isset($_SESSION['wcm_pos_cart']) ? $_SESSION['wcm_pos_cart'] : array();
    }

    /**
     * Save POS cart to session
     */
    private function save_pos_cart($cart) {
        if (!session_id()) {
            session_start();
        }

        $_SESSION['wcm_pos_cart'] = $cart;
    }

    /**
     * Calculate POS cart totals
     */
    private function calculate_pos_totals($cart) {
        $subtotal = 0;
        $tax = 0;

        foreach ($cart as $cart_item) {
            $line_total = $cart_item['price'] * $cart_item['quantity'];
            $subtotal += $line_total;

            // Calculate tax if enabled
            if (wc_tax_enabled()) {
                $product_id = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
                $product = wc_get_product($product_id);

                if ($product && $product->is_taxable()) {
                    $tax_rates = WC_Tax::get_rates($product->get_tax_class());
                    $line_tax = WC_Tax::calc_tax($line_total, $tax_rates, false);
                    $tax += array_sum($line_tax);
                }
            }
        }

        $total = $subtotal + $tax;

        return array(
            'subtotal' => $subtotal,
            'tax' => $tax,
            'total' => $total
        );
    }

    /**
     * Add cash transaction to cash manager
     */
    private function add_cash_transaction($amount, $order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;

        // Add as negative expense (income)
        $wpdb->insert(
            $table_name,
            array(
                'title' => sprintf(__('POS Sale - Order #%s', 'woo-cash-manager'), $order_id),
                'amount' => -$amount, // Negative amount for income
                'category' => 'POS Sales',
                'note' => sprintf(__('Cash payment from POS order #%s', 'woo-cash-manager'), $order_id),
                'expense_date' => current_time('Y-m-d')
            ),
            array('%s', '%f', '%s', '%s', '%s')
        );
    }
}
