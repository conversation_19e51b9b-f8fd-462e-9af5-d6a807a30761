<?php
/**
 * POS Dashboard Template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get message from URL parameter
$message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
?>

<div class="wrap wcm-pos-dashboard">
    <div class="wcm-pos-header">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-store"></span>
            <?php _e('Point of Sale System', 'woo-cash-manager'); ?>
        </h1>
        <a href="<?php echo admin_url('admin.php?page=woo-cash-manager'); ?>" class="button button-secondary">
            <span class="dashicons dashicons-arrow-left-alt"></span>
            <?php _e('Back to Dashboard', 'woo-cash-manager'); ?>
        </a>
    </div>
    <hr class="wp-header-end">

    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible">
            <p>
                <?php
                switch ($message) {
                    case 'order_created':
                        _e('Order created successfully!', 'woo-cash-manager');
                        break;
                    case 'payment_processed':
                        _e('Payment processed successfully!', 'woo-cash-manager');
                        break;
                    default:
                        echo esc_html($message);
                }
                ?>
            </p>
        </div>
    <?php endif; ?>

    <div class="wcm-pos-container">
        <!-- Products Section -->
        <div class="wcm-pos-products-section">
            <!-- Search and Filters -->
            <div class="wcm-pos-filters">
                <div class="wcm-pos-search-container">
                    <input type="text" id="wcm-pos-search" placeholder="<?php _e('Search products...', 'woo-cash-manager'); ?>" class="wcm-pos-search-input">
                    <button type="button" id="wcm-pos-search-btn" class="wcm-pos-search-btn">
                        <span class="dashicons dashicons-search"></span>
                    </button>
                </div>

                <div class="wcm-pos-filter-buttons">
                    <button type="button" class="wcm-pos-filter-btn active" data-filter="all"><?php _e('All', 'woo-cash-manager'); ?></button>
                    <button type="button" class="wcm-pos-filter-btn" data-filter="simple"><?php _e('Simple', 'woo-cash-manager'); ?></button>
                    <button type="button" class="wcm-pos-filter-btn" data-filter="variable"><?php _e('Variable', 'woo-cash-manager'); ?></button>
                </div>

                <div class="wcm-pos-view-toggle">
                    <button type="button" id="wcm-pos-grid-view" class="wcm-pos-view-btn active" data-view="grid">
                        <span class="dashicons dashicons-grid-view"></span>
                    </button>
                    <button type="button" id="wcm-pos-list-view" class="wcm-pos-view-btn" data-view="list">
                        <span class="dashicons dashicons-list-view"></span>
                    </button>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="wcm-pos-products-container" id="wcm-pos-products-grid">
                <div class="wcm-pos-loading" id="wcm-pos-loading">
                    <div class="wcm-spinner"></div>
                    <p><?php _e('Loading products...', 'woo-cash-manager'); ?></p>
                </div>

                <div class="wcm-pos-products-grid" id="wcm-pos-products-list">
                    <?php foreach ($products as $product): ?>
                        <div class="wcm-pos-product-card" data-product-id="<?php echo $product['id']; ?>" data-product-type="<?php echo $product['type']; ?>">
                            <div class="wcm-pos-product-image">
                                <?php if ($product['image_url']): ?>
                                    <img src="<?php echo esc_url($product['image_url']); ?>" alt="<?php echo esc_attr($product['name']); ?>">
                                <?php else: ?>
                                    <div class="wcm-pos-no-image">
                                        <span class="dashicons dashicons-format-image"></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($product['sale_price']): ?>
                                    <div class="wcm-pos-sale-badge"><?php _e('Sale', 'woo-cash-manager'); ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="wcm-pos-product-info">
                                <h4 class="wcm-pos-product-name"><?php echo esc_html($product['name']); ?></h4>
                                <div class="wcm-pos-product-sku"><?php echo esc_html($product['sku']); ?></div>

                                <div class="wcm-pos-product-price">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="wcm-pos-regular-price"><?php echo wc_price($product['regular_price']); ?></span>
                                        <span class="wcm-pos-sale-price"><?php echo wc_price($product['sale_price']); ?></span>
                                    <?php else: ?>
                                        <span class="wcm-pos-price"><?php echo wc_price($product['price']); ?></span>
                                    <?php endif; ?>
                                </div>

                                <div class="wcm-pos-product-stock">
                                    <?php if ($product['manage_stock']): ?>
                                        <span class="wcm-pos-stock-qty"><?php printf(__('Stock: %d', 'woo-cash-manager'), $product['stock_quantity']); ?></span>
                                    <?php else: ?>
                                        <span class="wcm-pos-stock-status"><?php _e('In Stock', 'woo-cash-manager'); ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($product['type'] === 'variable' && !empty($product['variations'])): ?>
                                    <div class="wcm-pos-variations">
                                        <select class="wcm-pos-variation-select" data-product-id="<?php echo $product['id']; ?>">
                                            <option value=""><?php _e('Select variation...', 'woo-cash-manager'); ?></option>
                                            <?php foreach ($product['variations'] as $variation): ?>
                                                <option value="<?php echo $variation['id']; ?>" data-price="<?php echo $variation['price']; ?>">
                                                    <?php
                                                    $attributes = array();
                                                    foreach ($variation['attributes'] as $attr_name => $attr_value) {
                                                        $attributes[] = ucfirst(str_replace('attribute_', '', $attr_name)) . ': ' . $attr_value;
                                                    }
                                                    echo esc_html(implode(', ', $attributes));
                                                    ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                <?php endif; ?>

                                <button type="button" class="wcm-pos-add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                    <span class="dashicons dashicons-cart"></span>
                                    <?php _e('Add to Cart', 'woo-cash-manager'); ?>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Cart Section -->
        <div class="wcm-pos-cart-section">
            <div class="wcm-card wcm-pos-cart-card">
                <div class="wcm-card-header">
                    <h3><span class="dashicons dashicons-cart"></span><?php _e('Shopping Cart', 'woo-cash-manager'); ?></h3>
                    <button type="button" id="wcm-pos-clear-cart" class="wcm-pos-clear-btn">
                        <span class="dashicons dashicons-trash"></span>
                        <?php _e('Clear', 'woo-cash-manager'); ?>
                    </button>
                </div>

                <div class="wcm-pos-cart-items" id="wcm-pos-cart-items">
                    <div class="wcm-pos-empty-cart">
                        <span class="dashicons dashicons-cart"></span>
                        <p><?php _e('Cart is empty', 'woo-cash-manager'); ?></p>
                    </div>
                </div>

                <!-- Cart Totals -->
                <div class="wcm-pos-cart-totals" id="wcm-pos-cart-totals">
                    <div class="wcm-pos-total-row">
                        <span><?php _e('Subtotal:', 'woo-cash-manager'); ?></span>
                        <span id="wcm-pos-subtotal"><?php echo wc_price(0); ?></span>
                    </div>
                    <div class="wcm-pos-total-row">
                        <span><?php _e('Tax:', 'woo-cash-manager'); ?></span>
                        <span id="wcm-pos-tax"><?php echo wc_price(0); ?></span>
                    </div>
                    <div class="wcm-pos-total-row wcm-pos-total-final">
                        <span><?php _e('Total:', 'woo-cash-manager'); ?></span>
                        <span id="wcm-pos-total"><?php echo wc_price(0); ?></span>
                    </div>
                </div>
            </div>

            <!-- Payment Section -->
            <div class="wcm-card wcm-pos-payment-card">
                <div class="wcm-card-header">
                    <h3><span class="dashicons dashicons-money-alt"></span><?php _e('Payment', 'woo-cash-manager'); ?></h3>
                </div>

                <div class="wcm-pos-payment-methods">
                    <?php foreach ($payment_methods as $method): ?>
                        <label class="wcm-pos-payment-method">
                            <input type="radio" name="payment_method" value="<?php echo esc_attr($method['id']); ?>" <?php checked($method['id'], 'cash'); ?>>
                            <span class="wcm-pos-payment-method-content">
                                <span class="dashicons <?php echo esc_attr($method['icon']); ?>"></span>
                                <span class="wcm-pos-payment-method-title"><?php echo esc_html($method['title']); ?></span>
                            </span>
                        </label>
                    <?php endforeach; ?>
                </div>

                <div class="wcm-pos-customer-info">
                    <label for="wcm-pos-customer-email"><?php _e('Customer Email (Optional):', 'woo-cash-manager'); ?></label>
                    <input type="email" id="wcm-pos-customer-email" placeholder="<?php _e('<EMAIL>', 'woo-cash-manager'); ?>">
                </div>

                <div class="wcm-pos-actions">
                    <button type="button" id="wcm-pos-process-order" class="button button-primary button-large" disabled>
                        <span class="dashicons dashicons-yes"></span>
                        <?php _e('Process Order', 'woo-cash-manager'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Receipt Modal -->
<div id="wcm-pos-receipt-modal" class="wcm-modal" style="display: none;">
    <div class="wcm-modal-content wcm-pos-receipt-content">
        <div class="wcm-modal-header">
            <h2><?php _e('Order Receipt', 'woo-cash-manager'); ?></h2>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body" id="wcm-pos-receipt-body">
            <!-- Receipt content will be populated by JavaScript -->
        </div>
        <div class="wcm-modal-footer">
            <button type="button" id="wcm-pos-print-receipt" class="button button-primary">
                <span class="dashicons dashicons-printer"></span>
                <?php _e('Print Receipt', 'woo-cash-manager'); ?>
            </button>
            <button type="button" class="button wcm-modal-close"><?php _e('Close', 'woo-cash-manager'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
// Pass PHP data to JavaScript
window.wcmPosData = {
    products: <?php echo json_encode($products); ?>,
    cartItems: <?php echo json_encode($cart_items); ?>,
    cartTotals: <?php echo json_encode($cart_totals); ?>,
    paymentMethods: <?php echo json_encode($payment_methods); ?>,
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('wcm_pos_nonce'); ?>',
    strings: {
        confirmClearCart: '<?php _e('Are you sure you want to clear the cart?', 'woo-cash-manager'); ?>',
        orderProcessing: '<?php _e('Processing order...', 'woo-cash-manager'); ?>',
        orderSuccess: '<?php _e('Order processed successfully!', 'woo-cash-manager'); ?>',
        orderError: '<?php _e('Error processing order. Please try again.', 'woo-cash-manager'); ?>',
        selectVariation: '<?php _e('Please select a variation for variable products.', 'woo-cash-manager'); ?>',
        emptyCart: '<?php _e('Cart is empty. Please add products to continue.', 'woo-cash-manager'); ?>',
        selectPayment: '<?php _e('Please select a payment method.', 'woo-cash-manager'); ?>'
    }
};
</script>
