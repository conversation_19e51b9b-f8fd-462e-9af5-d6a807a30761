/**
 * Admin JavaScript for Woo Cash Manager
 */

(function($) {
    'use strict';
    
    var WCMAdmin = {
        chart: null,
        
        init: function() {
            this.initChart();
            this.bindEvents();
        },
        
        bindEvents: function() {
            // Refresh dashboard
            $(document).on('click', '#wcm-refresh-dashboard', this.refreshDashboard);

            // Update chart
            $(document).on('click', '#wcm-update-chart', this.updateChart);

            // Modal triggers
            $(document).on('click', '#wcm-add-expense-btn, .wcm-add-expense-trigger', this.showAddExpenseModal);
            $(document).on('click', '.wcm-edit-expense-btn', this.showEditExpenseModal);
            $(document).on('click', '.wcm-duplicate-expense-btn', this.duplicateExpense);

            // Delete expense
            $(document).on('click', '.wcm-delete-btn', this.showDeleteModal);

            // Modal events
            $(document).on('click', '.wcm-modal-close, #wcm-modal-cancel, #wcm-cancel-delete', this.hideModal);
            $(document).on('click', '#wcm-confirm-delete', this.confirmDelete);

            // Close modal on outside click
            $(document).on('click', '.wcm-modal', function(e) {
                if (e.target === this) {
                    WCMAdmin.hideModal();
                }
            });

            // Form submission
            $(document).on('submit', '#wcm-expense-form', this.handleExpenseForm);

            // Quick category buttons
            $(document).on('click', '.wcm-category-btn', this.selectQuickCategory);

            // Refresh expenses
            $(document).on('click', '#wcm-refresh-expenses', this.refreshExpenses);

            // View All Expenses button
            $(document).on('click', '.wcm-view-all-expenses', this.scrollToExpenses);

            // Filters
            $(document).on('click', '#wcm-apply-filters', this.applyFilters);
            $(document).on('click', '#wcm-clear-filters', this.clearFilters);

            // Actions dropdown
            $(document).on('click', '.wcm-actions-toggle', this.toggleActionsMenu);
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.wcm-actions-dropdown').length) {
                    $('.wcm-actions-dropdown').removeClass('active');
                }
            });

            // Table sorting
            $(document).on('click', '.wcm-expenses-table th.sortable', this.sortTable);

            // Export expenses
            $(document).on('click', '#wcm-export-expenses', this.exportExpenses);
        },
        
        initChart: function() {
            var ctx = document.getElementById('wcm-chart');
            if (!ctx || typeof Chart === 'undefined') {
                return;
            }
            
            var chartData = window.wcmChartData || {
                labels: [],
                income: [],
                expenses: []
            };
            
            this.chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'Income',
                        data: chartData.income,
                        backgroundColor: 'rgba(0, 163, 42, 0.8)',
                        borderColor: 'rgba(0, 163, 42, 1)',
                        borderWidth: 1
                    }, {
                        label: 'Expenses',
                        data: chartData.expenses,
                        backgroundColor: 'rgba(214, 54, 56, 0.8)',
                        borderColor: 'rgba(214, 54, 56, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('en-US', {
                                        style: 'currency',
                                        currency: 'USD'
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + 
                                           new Intl.NumberFormat('en-US', {
                                               style: 'currency',
                                               currency: 'USD'
                                           }).format(context.parsed.y);
                                }
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        },
        
        refreshDashboard: function(e) {
            e.preventDefault();

            var $btn = $(this);
            var $icon = $btn.find('.dashicons');

            // Add loading state with animation
            $btn.addClass('wcm-loading');
            $icon.addClass('wcm-loading');

            // Add visual feedback
            $('.wcm-cards-container').css('opacity', '0.7');

            $.ajax({
                url: wcm_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_refresh_dashboard',
                    nonce: wcm_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Animate the updates
                        $('#wcm-balance').fadeOut(200, function() {
                            $(this).html(response.data.balance).fadeIn(200);
                        });
                        $('#wcm-income').fadeOut(200, function() {
                            $(this).html(response.data.income).fadeIn(200);
                        });
                        $('#wcm-expenses').fadeOut(200, function() {
                            $(this).html(response.data.expenses).fadeIn(200);
                        });

                        // Update card status with animation
                        var $balanceCard = $('.wcm-card-balance');
                        $balanceCard.removeClass('positive negative');
                        setTimeout(function() {
                            $balanceCard.addClass(response.data.balance_raw >= 0 ? 'positive' : 'negative');
                        }, 200);

                        WCMAdmin.showNotice('Dashboard refreshed successfully!', 'success');
                    } else {
                        WCMAdmin.showNotice('Failed to refresh dashboard.', 'error');
                    }
                },
                error: function() {
                    WCMAdmin.showNotice('An error occurred while refreshing.', 'error');
                },
                complete: function() {
                    $btn.removeClass('wcm-loading');
                    $icon.removeClass('wcm-loading');
                    $('.wcm-cards-container').css('opacity', '1');
                }
            });
        },
        
        updateChart: function(e) {
            e.preventDefault();
            
            var period = $('#wcm-chart-period').val();
            var $btn = $(this);
            
            $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> Updating...');
            
            $.ajax({
                url: wcm_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_get_chart_data',
                    period: period,
                    nonce: wcm_ajax.nonce
                },
                success: function(response) {
                    if (response.success && WCMAdmin.chart) {
                        WCMAdmin.chart.data.labels = response.data.labels;
                        WCMAdmin.chart.data.datasets[0].data = response.data.income;
                        WCMAdmin.chart.data.datasets[1].data = response.data.expenses;
                        WCMAdmin.chart.update();
                        
                        WCMAdmin.showNotice('Chart updated successfully!', 'success');
                    } else {
                        WCMAdmin.showNotice('Failed to update chart.', 'error');
                    }
                },
                error: function() {
                    WCMAdmin.showNotice('An error occurred while updating chart.', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('Update Chart');
                }
            });
        },
        
        showDeleteModal: function(e) {
            e.preventDefault();
            
            var expenseId = $(this).data('expense-id');
            var $modal = $('#wcm-delete-modal');
            
            $modal.data('expense-id', expenseId).show();
        },
        
        showAddExpenseModal: function(e) {
            e.preventDefault();

            // Reset form
            $('#wcm-expense-form')[0].reset();
            $('#wcm-expense-id').val('');
            $('#wcm-form-action').attr('name', 'wcm_add_expense').val('1');
            $('#wcm-modal-title').text('Add New Expense');
            $('#wcm-modal-save').html('<span class="dashicons dashicons-plus-alt"></span> Add Expense');
            $('#wcm-modal-date').val(new Date().toISOString().split('T')[0]);

            // Clear error states
            $('.wcm-input, .wcm-textarea').removeClass('wcm-error');

            // Show modal
            $('#wcm-expense-modal').addClass('wcm-modal-show').show();

            // Focus on title field
            setTimeout(function() {
                $('#wcm-modal-title-field').focus();
            }, 300);
        },

        showEditExpenseModal: function(e) {
            e.preventDefault();

            var $btn = $(this);
            var expenseId = $btn.data('expense-id');

            // Get expense data from data attributes (more reliable)
            var title = $btn.data('expense-title');
            var amount = $btn.data('expense-amount');
            var category = $btn.data('expense-category');
            var note = $btn.data('expense-note');
            var date = $btn.data('expense-date');

            // Fill form
            $('#wcm-modal-title-field').val(title);
            $('#wcm-modal-amount').val(amount);
            $('#wcm-modal-category').val(category);
            $('#wcm-modal-note').val(note);
            $('#wcm-modal-date').val(date);
            $('#wcm-expense-id').val(expenseId);
            $('#wcm-form-action').attr('name', 'wcm_update_expense').val('1');
            $('#wcm-modal-title').text('Edit Expense');
            $('#wcm-modal-save').html('<span class="dashicons dashicons-update"></span> Update Expense');

            // Clear error states
            $('.wcm-input, .wcm-textarea').removeClass('wcm-error');

            // Show modal
            $('#wcm-expense-modal').addClass('wcm-modal-show').show();

            // Focus on title field
            setTimeout(function() {
                $('#wcm-modal-title-field').focus();
            }, 300);
        },

        duplicateExpense: function(e) {
            e.preventDefault();

            var $btn = $(this);

            // Get expense data from data attributes
            var title = $btn.data('expense-title') + ' (Copy)';
            var amount = $btn.data('expense-amount');
            var category = $btn.data('expense-category');
            var note = $btn.data('expense-note');

            // Reset form for new expense
            $('#wcm-expense-form')[0].reset();
            $('#wcm-expense-id').val('');
            $('#wcm-form-action').attr('name', 'wcm_add_expense').val('1');
            $('#wcm-modal-title').text('Duplicate Expense');
            $('#wcm-modal-save').html('<span class="dashicons dashicons-plus-alt"></span> Add Expense');

            // Fill form with duplicated data
            $('#wcm-modal-title-field').val(title);
            $('#wcm-modal-amount').val(amount);
            $('#wcm-modal-category').val(category);
            $('#wcm-modal-note').val(note);
            $('#wcm-modal-date').val(new Date().toISOString().split('T')[0]);

            // Clear error states
            $('.wcm-input, .wcm-textarea').removeClass('wcm-error');

            // Show modal
            $('#wcm-expense-modal').addClass('wcm-modal-show').show();

            // Focus on title field
            setTimeout(function() {
                $('#wcm-modal-title-field').focus().select();
            }, 300);
        },

        hideModal: function() {
            $('.wcm-modal').removeClass('wcm-modal-show');
            setTimeout(function() {
                $('.wcm-modal').hide();
            }, 300);
        },

        selectQuickCategory: function(e) {
            e.preventDefault();
            var category = $(this).data('category');
            $('#wcm-modal-category').val(category);

            // Visual feedback
            $('.wcm-category-btn').removeClass('wcm-category-active');
            $(this).addClass('wcm-category-active');
        },
        
        confirmDelete: function(e) {
            e.preventDefault();
            
            var expenseId = $('#wcm-delete-modal').data('expense-id');
            var $btn = $(this);
            
            $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> Deleting...');
            
            $.ajax({
                url: wcm_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcm_delete_expense',
                    expense_id: expenseId,
                    nonce: wcm_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Remove the row from table
                        $('tr[data-expense-id="' + expenseId + '"]').fadeOut(function() {
                            $(this).remove();
                        });
                        
                        WCMAdmin.hideModal();
                        WCMAdmin.showNotice('Expense deleted successfully!', 'success');
                        
                        // Refresh page if no expenses left
                        if ($('.wcm-expenses-table tbody tr').length === 1) {
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        WCMAdmin.showNotice('Failed to delete expense.', 'error');
                    }
                },
                error: function() {
                    WCMAdmin.showNotice('An error occurred while deleting.', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('Delete');
                }
            });
        },
        
        handleExpenseForm: function(e) {
            e.preventDefault();

            var $form = $(this);
            var isValid = WCMAdmin.validateExpenseForm($form);

            if (!isValid) {
                return false;
            }

            // Add loading state
            var $submitBtn = $('#wcm-modal-save');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<span class="wcm-spinner"></span> Saving...');

            // Submit form
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    // Hide modal
                    WCMAdmin.hideModal();

                    // Show success message
                    WCMAdmin.showNotice('Expense saved successfully!', 'success');

                    // Reload page to show updated data
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                },
                error: function() {
                    WCMAdmin.showNotice('Failed to save expense. Please try again.', 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).html(originalText);
                }
            });
        },

        validateExpenseForm: function($form) {
            var isValid = true;
            var errors = [];

            // Clear previous error styling
            $form.find('.wcm-input, .wcm-textarea').removeClass('wcm-error');

            // Validate title
            var $titleField = $form.find('input[name="title"]');
            var title = $titleField.val().trim();
            if (!title) {
                errors.push('Title is required.');
                $titleField.addClass('wcm-error');
                isValid = false;
            }

            // Validate amount
            var $amountField = $form.find('input[name="amount"]');
            var amount = parseFloat($amountField.val());
            if (!amount || amount <= 0) {
                errors.push('Amount must be greater than 0.');
                $amountField.addClass('wcm-error');
                isValid = false;
            }

            // Validate category
            var $categoryField = $form.find('input[name="category"]');
            var category = $categoryField.val().trim();
            if (!category) {
                errors.push('Category is required.');
                $categoryField.addClass('wcm-error');
                isValid = false;
            }

            // Validate date
            var $dateField = $form.find('input[name="expense_date"]');
            var date = $dateField.val();
            if (!date) {
                errors.push('Expense date is required.');
                $dateField.addClass('wcm-error');
                isValid = false;
            }

            if (!isValid) {
                WCMAdmin.showNotice(errors.join('<br>'), 'error');

                // Focus on first error field
                $form.find('.wcm-error').first().focus();
            }

            return isValid;
        },

        refreshExpenses: function(e) {
            e.preventDefault();

            var $btn = $(this);
            var $icon = $btn.find('.dashicons');

            // Add loading state
            $btn.prop('disabled', true);
            $icon.addClass('wcm-loading');

            // Reload the page to refresh expenses
            setTimeout(function() {
                window.location.reload();
            }, 500);
        },

        scrollToExpenses: function(e) {
            e.preventDefault();

            var target = $(this).data('target');
            var $target = $('#' + target);

            if ($target.length) {
                $('html, body').animate({
                    scrollTop: $target.offset().top - 50
                }, 800, 'easeInOutCubic');

                // Add highlight effect
                $target.addClass('wcm-highlight');
                setTimeout(function() {
                    $target.removeClass('wcm-highlight');
                }, 2000);
            }
        },

        applyFilters: function(e) {
            e.preventDefault();

            var search = $('#wcm-search-expenses').val();
            var category = $('#wcm-filter-category').val();
            var dateFrom = $('#wcm-filter-date-from').val();
            var dateTo = $('#wcm-filter-date-to').val();

            var params = new URLSearchParams(window.location.search);

            // Update URL parameters
            if (search) params.set('search', search);
            else params.delete('search');

            if (category) params.set('category', category);
            else params.delete('category');

            if (dateFrom) params.set('date_from', dateFrom);
            else params.delete('date_from');

            if (dateTo) params.set('date_to', dateTo);
            else params.delete('date_to');

            // Reset pagination
            params.delete('paged');

            // Reload page with new filters
            window.location.search = params.toString();
        },

        clearFilters: function(e) {
            e.preventDefault();

            // Clear filter inputs
            $('#wcm-search-expenses').val('');
            $('#wcm-filter-category').val('');
            $('#wcm-filter-date-from').val('');
            $('#wcm-filter-date-to').val('');

            // Remove filter parameters from URL
            var params = new URLSearchParams(window.location.search);
            params.delete('search');
            params.delete('category');
            params.delete('date_from');
            params.delete('date_to');
            params.delete('paged');

            // Reload page without filters
            window.location.search = params.toString();
        },

        toggleActionsMenu: function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $dropdown = $(this).closest('.wcm-actions-dropdown');

            // Close other dropdowns
            $('.wcm-actions-dropdown').not($dropdown).removeClass('active');

            // Toggle current dropdown
            $dropdown.toggleClass('active');
        },

        sortTable: function(e) {
            e.preventDefault();

            var $th = $(this);
            var sortBy = $th.data('sort');
            var currentSort = new URLSearchParams(window.location.search).get('sort');
            var currentOrder = new URLSearchParams(window.location.search).get('order');

            var newOrder = 'asc';
            if (currentSort === sortBy && currentOrder === 'asc') {
                newOrder = 'desc';
            }

            var params = new URLSearchParams(window.location.search);
            params.set('sort', sortBy);
            params.set('order', newOrder);
            params.delete('paged');

            window.location.search = params.toString();
        },

        exportExpenses: function(e) {
            e.preventDefault();

            WCMAdmin.showNotice('Export feature coming soon!', 'info');
        },
        
        showNotice: function(message, type) {
            type = type || 'info';

            // Create modern notification
            var iconClass = type === 'success' ? 'dashicons-yes-alt' :
                           type === 'error' ? 'dashicons-dismiss' : 'dashicons-info';

            var $notice = $('<div class="wcm-modern-notice wcm-notice-' + type + '">' +
                '<div class="wcm-notice-icon"><span class="dashicons ' + iconClass + '"></span></div>' +
                '<div class="wcm-notice-content">' + message + '</div>' +
                '<div class="wcm-notice-close"><span class="dashicons dashicons-no-alt"></span></div>' +
                '</div>');

            // Remove existing notices
            $('.wcm-modern-notice').remove();
            $('.notice').remove();

            // Add new notice with animation
            $('body').append($notice);

            // Animate in
            setTimeout(function() {
                $notice.addClass('wcm-notice-show');
            }, 100);

            // Close button functionality
            $notice.find('.wcm-notice-close').on('click', function() {
                $notice.removeClass('wcm-notice-show');
                setTimeout(function() {
                    $notice.remove();
                }, 300);
            });

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                if ($notice.length) {
                    $notice.removeClass('wcm-notice-show');
                    setTimeout(function() {
                        $notice.remove();
                    }, 300);
                }
            }, 5000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        WCMAdmin.init();

        // Initialize POS if on POS page
        if (typeof window.wcmPosData !== 'undefined') {
            WCMPOS.init();
        }
    });

    // Expose to global scope for debugging
    window.WCMAdmin = WCMAdmin;

    /**
     * POS System JavaScript
     */
    var WCMPOS = {
        cart: {},
        products: [],

        init: function() {
            this.products = window.wcmPosData.products || [];
            this.cart = window.wcmPosData.cartItems || {};
            this.bindEvents();
            this.updateCartDisplay();
        },

        bindEvents: function() {
            // Product search
            $(document).on('input', '#wcm-pos-search', this.debounce(this.searchProducts, 300));
            $(document).on('click', '#wcm-pos-search-btn', this.searchProducts);

            // Product filters
            $(document).on('click', '.wcm-pos-filter-btn', this.filterProducts);

            // View toggle
            $(document).on('click', '.wcm-pos-view-btn', this.toggleView);

            // Add to cart
            $(document).on('click', '.wcm-pos-add-to-cart', this.addToCart);

            // Cart management
            $(document).on('click', '.wcm-pos-qty-btn', this.updateQuantity);
            $(document).on('change', '.wcm-pos-qty-input', this.updateQuantityInput);
            $(document).on('click', '.wcm-pos-remove-item', this.removeCartItem);
            $(document).on('click', '#wcm-pos-clear-cart', this.clearCart);

            // Payment
            $(document).on('change', 'input[name="payment_method"]', this.updateProcessButton);
            $(document).on('click', '#wcm-pos-process-order', this.processOrder);

            // Receipt modal
            $(document).on('click', '.wcm-modal-close', this.closeModal);
            $(document).on('click', '#wcm-pos-print-receipt', this.printReceipt);

            // Variation selection
            $(document).on('change', '.wcm-pos-variation-select', this.updateVariationPrice);
        },

        searchProducts: function() {
            var search = $('#wcm-pos-search').val();

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_search_products',
                    search: search,
                    nonce: window.wcmPosData.nonce
                },
                beforeSend: function() {
                    $('#wcm-pos-loading').show();
                    $('#wcm-pos-products-list').hide();
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.products = response.data;
                        WCMPOS.renderProducts();
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice('Search failed. Please try again.', 'error');
                },
                complete: function() {
                    $('#wcm-pos-loading').hide();
                    $('#wcm-pos-products-list').show();
                }
            });
        },

        filterProducts: function(e) {
            var filter = $(e.target).data('filter');

            $('.wcm-pos-filter-btn').removeClass('active');
            $(e.target).addClass('active');

            if (filter === 'all') {
                $('.wcm-pos-product-card').show();
            } else {
                $('.wcm-pos-product-card').hide();
                $('.wcm-pos-product-card[data-product-type="' + filter + '"]').show();
            }
        },

        toggleView: function(e) {
            var view = $(e.target).closest('.wcm-pos-view-btn').data('view');

            $('.wcm-pos-view-btn').removeClass('active');
            $(e.target).closest('.wcm-pos-view-btn').addClass('active');

            if (view === 'list') {
                $('.wcm-pos-products-grid').addClass('wcm-pos-list-view');
            } else {
                $('.wcm-pos-products-grid').removeClass('wcm-pos-list-view');
            }
        },

        addToCart: function(e) {
            var $btn = $(e.target).closest('.wcm-pos-add-to-cart');
            var productId = $btn.data('product-id');
            var $card = $btn.closest('.wcm-pos-product-card');
            var $variationSelect = $card.find('.wcm-pos-variation-select');
            var variationId = 0;

            // Check if variation is required and selected
            if ($variationSelect.length && !$variationSelect.val()) {
                WCMPOS.showNotice(window.wcmPosData.strings.selectVariation, 'error');
                return;
            }

            if ($variationSelect.length) {
                variationId = $variationSelect.val();
            }

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_add_to_cart',
                    product_id: productId,
                    variation_id: variationId,
                    quantity: 1,
                    nonce: window.wcmPosData.nonce
                },
                beforeSend: function() {
                    $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> Adding...');
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.cart = response.data.cart;
                        WCMPOS.updateCartDisplay();
                        WCMPOS.updateTotals(response.data.totals);
                        WCMPOS.showNotice('Product added to cart', 'success');
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice('Failed to add product to cart', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-cart"></span> Add to Cart');
                }
            });
        },

        updateQuantity: function(e) {
            var $btn = $(e.target);
            var $qtyInput = $btn.siblings('.wcm-pos-qty-input');
            var currentQty = parseInt($qtyInput.val()) || 0;
            var newQty = $btn.hasClass('wcm-pos-qty-plus') ? currentQty + 1 : Math.max(0, currentQty - 1);

            $qtyInput.val(newQty);
            WCMPOS.updateCartItemQuantity($qtyInput);
        },

        updateQuantityInput: function(e) {
            WCMPOS.updateCartItemQuantity($(e.target));
        },

        updateCartItemQuantity: function($input) {
            var cartItemKey = $input.data('cart-item-key');
            var quantity = parseInt($input.val()) || 0;

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_update_cart_item',
                    cart_item_key: cartItemKey,
                    quantity: quantity,
                    nonce: window.wcmPosData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.cart = response.data.cart;
                        WCMPOS.updateCartDisplay();
                        WCMPOS.updateTotals(response.data.totals);
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice('Failed to update quantity', 'error');
                }
            });
        },

        removeCartItem: function(e) {
            var cartItemKey = $(e.target).closest('.wcm-pos-remove-item').data('cart-item-key');

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_remove_cart_item',
                    cart_item_key: cartItemKey,
                    nonce: window.wcmPosData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.cart = response.data.cart;
                        WCMPOS.updateCartDisplay();
                        WCMPOS.updateTotals(response.data.totals);
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice('Failed to remove item', 'error');
                }
            });
        },

        clearCart: function() {
            if (!confirm(window.wcmPosData.strings.confirmClearCart)) {
                return;
            }

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_clear_cart',
                    nonce: window.wcmPosData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.cart = {};
                        WCMPOS.updateCartDisplay();
                        WCMPOS.updateTotals(response.data.totals);
                        WCMPOS.showNotice('Cart cleared', 'success');
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice('Failed to clear cart', 'error');
                }
            });
        },

        updateProcessButton: function() {
            var hasItems = Object.keys(WCMPOS.cart).length > 0;
            var hasPaymentMethod = $('input[name="payment_method"]:checked').length > 0;

            $('#wcm-pos-process-order').prop('disabled', !hasItems || !hasPaymentMethod);
        },

        processOrder: function() {
            var paymentMethod = $('input[name="payment_method"]:checked').val();
            var customerEmail = $('#wcm-pos-customer-email').val();

            if (!paymentMethod) {
                WCMPOS.showNotice(window.wcmPosData.strings.selectPayment, 'error');
                return;
            }

            if (Object.keys(WCMPOS.cart).length === 0) {
                WCMPOS.showNotice(window.wcmPosData.strings.emptyCart, 'error');
                return;
            }

            var $btn = $('#wcm-pos-process-order');

            $.ajax({
                url: window.wcmPosData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'wcm_pos_process_order',
                    payment_method: paymentMethod,
                    customer_email: customerEmail,
                    nonce: window.wcmPosData.nonce
                },
                beforeSend: function() {
                    $btn.prop('disabled', true).html('<span class="wcm-spinner"></span> ' + window.wcmPosData.strings.orderProcessing);
                },
                success: function(response) {
                    if (response.success) {
                        WCMPOS.cart = {};
                        WCMPOS.updateCartDisplay();
                        WCMPOS.updateTotals({subtotal: 0, tax: 0, total: 0});
                        WCMPOS.showReceipt(response.data);
                        WCMPOS.showNotice(window.wcmPosData.strings.orderSuccess, 'success');

                        // Reset form
                        $('#wcm-pos-customer-email').val('');
                        $('input[name="payment_method"]').prop('checked', false);
                        $('input[name="payment_method"][value="cash"]').prop('checked', true);
                    } else {
                        WCMPOS.showNotice(response.data, 'error');
                    }
                },
                error: function() {
                    WCMPOS.showNotice(window.wcmPosData.strings.orderError, 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-yes"></span> Process Order');
                    WCMPOS.updateProcessButton();
                }
            });
        },

        updateCartDisplay: function() {
            var $cartItems = $('#wcm-pos-cart-items');

            if (Object.keys(this.cart).length === 0) {
                $cartItems.html('<div class="wcm-pos-empty-cart"><span class="dashicons dashicons-cart"></span><p>Cart is empty</p></div>');
                this.updateProcessButton();
                return;
            }

            var html = '';
            for (var key in this.cart) {
                var item = this.cart[key];
                html += this.renderCartItem(key, item);
            }

            $cartItems.html(html);
            this.updateProcessButton();
        },

        renderCartItem: function(key, item) {
            var imageHtml = item.image_url ?
                '<img src="' + item.image_url + '" alt="' + item.name + '">' :
                '<span class="dashicons dashicons-format-image"></span>';

            return '<div class="wcm-pos-cart-item">' +
                '<div class="wcm-pos-cart-item-image">' + imageHtml + '</div>' +
                '<div class="wcm-pos-cart-item-info">' +
                    '<div class="wcm-pos-cart-item-name">' + item.name + '</div>' +
                    '<div class="wcm-pos-cart-item-price">' + this.formatPrice(item.price) + '</div>' +
                '</div>' +
                '<div class="wcm-pos-cart-item-controls">' +
                    '<button type="button" class="wcm-pos-qty-btn wcm-pos-qty-minus">-</button>' +
                    '<input type="number" class="wcm-pos-qty-input" value="' + item.quantity + '" min="1" data-cart-item-key="' + key + '">' +
                    '<button type="button" class="wcm-pos-qty-btn wcm-pos-qty-plus">+</button>' +
                    '<button type="button" class="wcm-pos-remove-item" data-cart-item-key="' + key + '">' +
                        '<span class="dashicons dashicons-trash"></span>' +
                    '</button>' +
                '</div>' +
            '</div>';
        },

        updateTotals: function(totals) {
            $('#wcm-pos-subtotal').text(this.formatPrice(totals.subtotal));
            $('#wcm-pos-tax').text(this.formatPrice(totals.tax));
            $('#wcm-pos-total').text(this.formatPrice(totals.total));
        },

        updateVariationPrice: function(e) {
            var $select = $(e.target);
            var $card = $select.closest('.wcm-pos-product-card');
            var $priceContainer = $card.find('.wcm-pos-product-price');
            var selectedOption = $select.find('option:selected');
            var price = selectedOption.data('price');

            if (price) {
                $priceContainer.html('<span class="wcm-pos-price">' + this.formatPrice(price) + '</span>');
            }
        },

        showReceipt: function(orderData) {
            var receiptHtml = this.generateReceiptHtml(orderData);
            $('#wcm-pos-receipt-body').html(receiptHtml);
            $('#wcm-pos-receipt-modal').show();
        },

        generateReceiptHtml: function(orderData) {
            var html = '<div class="wcm-pos-receipt">';
            html += '<div class="wcm-pos-receipt-header">';
            html += '<h3>RECEIPT</h3>';
            html += '<p>Order #' + orderData.order_number + '</p>';
            html += '<p>' + new Date().toLocaleString() + '</p>';
            html += '</div>';

            html += '<div class="wcm-pos-receipt-items">';
            for (var key in this.cart) {
                var item = this.cart[key];
                html += '<div class="wcm-pos-receipt-item">';
                html += '<span>' + item.name + '</span>';
                html += '<span>' + item.quantity + ' x ' + this.formatPrice(item.price) + '</span>';
                html += '<span>' + this.formatPrice(item.quantity * item.price) + '</span>';
                html += '</div>';
            }
            html += '</div>';

            html += '<div class="wcm-pos-receipt-total">';
            html += '<strong>Total: ' + this.formatPrice(orderData.total) + '</strong>';
            html += '</div>';

            html += '</div>';
            return html;
        },

        closeModal: function() {
            $('.wcm-modal').hide();
        },

        printReceipt: function() {
            var receiptContent = $('#wcm-pos-receipt-body').html();
            var printWindow = window.open('', '_blank');
            printWindow.document.write('<html><head><title>Receipt</title></head><body>' + receiptContent + '</body></html>');
            printWindow.document.close();
            printWindow.print();
        },

        renderProducts: function() {
            var html = '';
            for (var i = 0; i < this.products.length; i++) {
                html += this.renderProductCard(this.products[i]);
            }
            $('#wcm-pos-products-list').html(html);
        },

        renderProductCard: function(product) {
            // This would be a complex function to render product cards
            // For now, we'll rely on the server-side rendering
            return '';
        },

        formatPrice: function(price) {
            return '$' + parseFloat(price).toFixed(2);
        },

        showNotice: function(message, type) {
            // Use the existing WCMAdmin notice system
            if (typeof WCMAdmin !== 'undefined' && WCMAdmin.showNotice) {
                WCMAdmin.showNotice(message, type);
            } else {
                alert(message);
            }
        },

        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Expose WCMPOS to global scope
    window.WCMPOS = WCMPOS;

})(jQuery);
