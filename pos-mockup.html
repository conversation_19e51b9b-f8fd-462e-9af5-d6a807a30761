<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Manager POS System - Layout Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f1f1f1;
            padding: 20px;
        }
        
        .pos-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .pos-header {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .products-section {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-container {
            display: flex;
            flex: 1;
            max-width: 300px;
        }
        
        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-right: none;
            border-radius: 8px 0 0 8px;
            font-size: 14px;
        }
        
        .search-btn {
            padding: 10px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 0 8px 8px 0;
            cursor: pointer;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
        }
        
        .filter-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
        }
        
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            max-height: 600px;
            overflow-y: auto;
            padding: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #fff;
        }
        
        .product-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .product-card:hover::before {
            opacity: 1;
        }
        
        .product-image {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 24px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: 700;
            color: #27ae60;
            margin-bottom: 8px;
        }
        
        .product-stock {
            font-size: 12px;
            color: #27ae60;
            margin-bottom: 12px;
        }
        
        .add-to-cart-btn {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        
        .cart-section {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .cart-card, .payment-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            margin: 0;
            font-size: 16px;
        }
        
        .cart-items {
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .empty-cart {
            text-align: center;
            color: #7f8c8d;
            padding: 40px 20px;
        }
        
        .cart-totals {
            padding: 15px 20px;
            border-top: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .total-final {
            font-size: 16px;
            font-weight: 700;
            padding-top: 8px;
            border-top: 1px solid #e1e5e9;
            color: #2c3e50;
        }
        
        .payment-methods {
            padding: 20px;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-method.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }
        
        .process-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .process-btn:hover {
            background: linear-gradient(135deg, #229954 0%, #28b463 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }
        
        .process-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        @media (max-width: 992px) {
            .pos-container {
                grid-template-columns: 1fr;
            }
            
            .cart-section {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <div class="pos-header">
            <h1>🏪 Point of Sale System</h1>
            <button style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 8px 16px; border-radius: 6px; cursor: pointer;">← Back to Dashboard</button>
        </div>
        
        <!-- Products Section -->
        <div class="products-section">
            <div class="filters">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search products...">
                    <button class="search-btn">🔍</button>
                </div>
                
                <div class="filter-buttons">
                    <button class="filter-btn active">All</button>
                    <button class="filter-btn">Simple</button>
                    <button class="filter-btn">Variable</button>
                </div>
                
                <div style="display: flex; gap: 5px;">
                    <button style="padding: 8px 12px; background: #667eea; color: white; border: none; border-radius: 6px 0 0 6px; cursor: pointer;">⊞</button>
                    <button style="padding: 8px 12px; background: #f8f9fa; border: 1px solid #e1e5e9; border-radius: 0 6px 6px 0; cursor: pointer;">☰</button>
                </div>
            </div>
            
            <div class="products-grid">
                <!-- Sample Product Cards -->
                <div class="product-card">
                    <div class="product-image">📱</div>
                    <div class="product-name">iPhone 15 Pro</div>
                    <div class="product-price">$999.00</div>
                    <div class="product-stock">Stock: 25</div>
                    <button class="add-to-cart-btn">🛒 Add to Cart</button>
                </div>
                
                <div class="product-card">
                    <div class="product-image">💻</div>
                    <div class="product-name">MacBook Air M2</div>
                    <div class="product-price">$1,199.00</div>
                    <div class="product-stock">Stock: 15</div>
                    <button class="add-to-cart-btn">🛒 Add to Cart</button>
                </div>
                
                <div class="product-card">
                    <div class="product-image">🎧</div>
                    <div class="product-name">AirPods Pro</div>
                    <div class="product-price">$249.00</div>
                    <div class="product-stock">Stock: 50</div>
                    <button class="add-to-cart-btn">🛒 Add to Cart</button>
                </div>
                
                <div class="product-card">
                    <div class="product-image">⌚</div>
                    <div class="product-name">Apple Watch Series 9</div>
                    <div class="product-price">$399.00</div>
                    <div class="product-stock">Stock: 30</div>
                    <button class="add-to-cart-btn">🛒 Add to Cart</button>
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="cart-section">
            <!-- Shopping Cart -->
            <div class="cart-card">
                <div class="card-header">
                    <h3>🛒 Shopping Cart</h3>
                    <button style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">🗑️ Clear</button>
                </div>
                
                <div class="cart-items">
                    <div class="empty-cart">
                        <div style="font-size: 48px; margin-bottom: 10px; opacity: 0.5;">🛒</div>
                        <p>Cart is empty</p>
                    </div>
                </div>
                
                <div class="cart-totals">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span>$0.00</span>
                    </div>
                    <div class="total-row">
                        <span>Tax:</span>
                        <span>$0.00</span>
                    </div>
                    <div class="total-row total-final">
                        <span>Total:</span>
                        <span>$0.00</span>
                    </div>
                </div>
            </div>
            
            <!-- Payment Section -->
            <div class="payment-card">
                <div class="card-header">
                    <h3>💳 Payment</h3>
                </div>
                
                <div class="payment-methods">
                    <div class="payment-method selected">
                        <span style="font-size: 18px;">💵</span>
                        <span>Cash</span>
                    </div>
                    
                    <div class="payment-method">
                        <span style="font-size: 18px;">💳</span>
                        <span>Credit Card</span>
                    </div>
                    
                    <div class="payment-method">
                        <span style="font-size: 18px;">🏦</span>
                        <span>Bank Transfer</span>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #2c3e50;">Customer Email (Optional):</label>
                        <input type="email" placeholder="<EMAIL>" style="width: 100%; padding: 10px 12px; border: 1px solid #e1e5e9; border-radius: 6px; font-size: 14px;">
                    </div>
                </div>
                
                <div style="padding: 20px; border-top: 1px solid #e1e5e9; background: #f8f9fa;">
                    <button class="process-btn" disabled>
                        ✅ Process Order
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
